<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { setupDashboardScaleListener } from '@/utils/dashboard-scale';

defineOptions({
  name: 'DashboardContain'
});

interface Props {
  /** 设计宽度 */
  designWidth?: number;
  /** 设计高度 */
  designHeight?: number;
  /** 是否启用缩放 */
  enableScale?: boolean;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  designWidth: 1920,
  designHeight: 1080,
  enableScale: true
});

// 大屏容器引用
const containerRef = ref<HTMLElement | null>(null);

// 清理函数
let cleanupListener: (() => void) | null = null;

/**
 * 应用缩放
 */
const applyScale = () => {
  if (!containerRef.value) return;

  // 如果已有监听器，先清理
  if (cleanupListener) {
    cleanupListener();
  }

  // 设置新的监听器
  cleanupListener = setupDashboardScaleListener(containerRef.value, {
    enableScale: props.enableScale,
    designWidth: props.designWidth,
    designHeight: props.designHeight
  });
};

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  applyScale();
});

/**
 * 监听enableScale属性变化
 */
watch(
  () => props.enableScale,
  () => {
    applyScale();
  }
);

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  if (cleanupListener) {
    cleanupListener();
  }
});
</script>

<template>
  <div class="h-full w-full overflow-hidden bg-[#082761]">
    <!-- 主容器 -->
    <div
      ref="containerRef"
      class="z-999 flex flex-col origin-top-left transform-gpu transition-all duration-600 ease-out will-change-transform"
    >
      <slot />
    </div>
  </div>
</template>
