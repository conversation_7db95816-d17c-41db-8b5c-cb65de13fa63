/**
 * 大屏缩放工具函数
 */

/**
 * 计算大屏缩放比例
 * @param designWidth 设计宽度
 * @param designHeight 设计高度
 * @returns 缩放比例
 */
export function calculateScale(designWidth = 1920, designHeight = 1080): number {
  const ww = window.innerWidth / designWidth;
  const wh = window.innerHeight / designHeight;
  // 限制最小缩放比例，确保在小屏幕上不会过度缩小
  const scale = ww < wh ? ww : wh;
  return Math.max(scale, 0.5); // 设置最小缩放比例为0.5
}

/**
 * 缩放配置接口
 */
export interface ScaleOptions {
  enableScale?: boolean;
  designWidth?: number;
  designHeight?: number;
}

/**
 * 应用大屏缩放样式
 * @param element 目标DOM元素
 * @param options 缩放选项
 */
export function applyDashboardScale(element: HTMLElement, options: ScaleOptions = {}): void {
  if (!element) return;

  const { enableScale = true, designWidth = 1920, designHeight = 1080 } = options;

  if (enableScale) {
    // 缩放模式：固定尺寸，居中缩放
    const scale = calculateScale(designWidth, designHeight);
    element.style.cssText = `
      transform: scale(${scale}) translate(-50%, -50%);
      transform-origin: top left;
      overflow: hidden;
      width: ${designWidth}px;
      height: ${designHeight}px;
      position: fixed;
      left: 50%;
      top: 50%;
      transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    `;
  } else {
    // 非缩放模式：全屏显示，主容器不滚动
    element.style.cssText = `
      transform: none;
      transform-origin: top left;
      overflow: hidden;
      width: 100%;
      height: 100%;
      position: relative;
      left: 0;
      top: 0;
      transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    `;
  }
}

/**
 * 监听窗口大小变化并应用缩放
 * @param element 目标DOM元素
 * @param options 缩放选项
 * @returns 清理函数，用于移除事件监听
 */
export function setupDashboardScaleListener(element: HTMLElement, options: ScaleOptions = {}): () => void {
  // 应用初始缩放
  applyDashboardScale(element, options);

  // 添加一个延迟的二次调整，确保所有子元素都正确渲染和计算
  const initialAdjustTimeout = window.setTimeout(() => {
    // 重新应用一次缩放，确保所有元素都已完全渲染
    applyDashboardScale(element, options);
  }, 300);

  // 防抖处理
  let resizeTimeout: number | null = null;

  // 窗口大小变化处理函数
  const handleResize = () => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    resizeTimeout = window.setTimeout(() => {
      applyDashboardScale(element, options);
    }, 200); // 200ms防抖
  };

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);

  // 返回清理函数
  return () => {
    window.removeEventListener('resize', handleResize);
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    if (initialAdjustTimeout) {
      clearTimeout(initialAdjustTimeout);
    }
  };
}
