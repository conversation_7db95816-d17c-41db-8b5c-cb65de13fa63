<script setup lang="ts">
import { ref } from 'vue';
import { useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'CenterBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'info',
  animationDelay: 0
});

// 模拟数据 - 使用蓝色主题
const chartData = ref({
  networkQuality: {
    title: '网络质量',
    data: [85, 78], // 移动、电信
    color: '#1890ff',
    bgColor: 'rgba(24, 144, 255, 0.2)',
    borderColor: 'rgba(24, 144, 255, 0.5)'
  },
  internetQuality: {
    title: '上网质量',
    data: [92, 88], // 移动、电信
    color: '#40a9ff',
    bgColor: 'rgba(64, 169, 255, 0.2)',
    borderColor: 'rgba(64, 169, 255, 0.5)'
  },
  maintenanceService: {
    title: '装维服务',
    data: [76, 82], // 移动、电信
    color: '#69c0ff',
    bgColor: 'rgba(105, 192, 255, 0.2)',
    borderColor: 'rgba(105, 192, 255, 0.5)'
  },
  enterpriseProduct: {
    title: '政企产品质量',
    data: [89, 85], // 移动、电信
    color: '#096dd9',
    bgColor: 'rgba(9, 109, 217, 0.2)',
    borderColor: 'rgba(9, 109, 217, 0.5)'
  }
});

// 创建柱状图配置 - 数据大屏风格
const createBarChartOption = (title: string, data: number[], color: string) => ({
  title: {
    text: title,
    left: 'center',
    top: 0,
    textStyle: {
      color: '#e6f7ff',
      fontSize: 14,
      fontWeight: 600,
      textShadow: '0 0 5px rgba(0, 149, 255, 0.8)'
    }
  },
  tooltip: {
    trigger: 'axis' as const,
    backgroundColor: 'rgba(13, 41, 84, 0.9)',
    borderColor: color,
    borderWidth: 1,
    textStyle: {
      color: '#e6f7ff'
    },
    axisPointer: {
      type: 'shadow' as const,
      shadowStyle: {
        color: 'rgba(24, 144, 255, 0.1)'
      }
    },
    formatter: (params: any) => {
      const param = params[0];
      return `<div style="padding: 4px 8px;">
        <div style="font-weight: bold; margin-bottom: 4px;">${param.name}</div>
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <span style="margin-right: 12px;">满意度：</span>
          <span style="color: ${color}; font-weight: bold;">${param.value}%</span>
        </div>
      </div>`;
    }
  },
  grid: {
    left: '5%',
    right: '15%',
    top: '35%',
    bottom: '5%',
    containLabel: true
  },
  xAxis: {
    type: 'category' as const,
    data: ['移动', '电信'],
    axisLine: {
      lineStyle: {
        color: 'rgba(24, 144, 255, 0.3)',
        width: 1
      }
    },
    axisLabel: {
      color: '#e6f7ff',
      fontSize: 12,
      margin: 10
    },
    axisTick: {
      show: false
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value' as const,
    min: 0,
    max: 100,
    show: false,
    splitLine: {
      show: false
    }
  },
  series: [
    {
      type: 'bar' as const,
      data,
      barWidth: '40%',
      itemStyle: {
        color: {
          type: 'linear' as const,
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color
            },
            {
              offset: 1,
              color: 'rgba(24, 144, 255, 0.2)'
            }
          ]
        },
        borderRadius: [4, 4, 0, 0],
        shadowColor: color,
        shadowBlur: 10,
        shadowOffsetY: 2
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 20,
          shadowColor: `${color}`,
          opacity: 0.9,
          borderWidth: 1,
          borderColor: '#ffffff50'
        }
      },
      label: {
        show: true,
        position: 'top' as const,
        color,
        fontSize: 14,
        fontWeight: 600,
        formatter: '{c}%',
        textShadow: '0 0 3px rgba(0, 0, 0, 0.3)'
      }
    }
  ]
});

// 初始化四个图表
const { domRef: networkQualityRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.networkQuality.title,
    chartData.value.networkQuality.data,
    chartData.value.networkQuality.color
  )
);

const { domRef: internetQualityRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.internetQuality.title,
    chartData.value.internetQuality.data,
    chartData.value.internetQuality.color
  )
);

const { domRef: maintenanceServiceRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.maintenanceService.title,
    chartData.value.maintenanceService.data,
    chartData.value.maintenanceService.color
  )
);

const { domRef: enterpriseProductRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.enterpriseProduct.title,
    chartData.value.enterpriseProduct.data,
    chartData.value.enterpriseProduct.color
  )
);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
  >
    <!-- 四个柱状图的网格布局 - 数据大屏科技感风格 -->
    <div class="grid grid-cols-2 grid-rows-2 h-99% gap-12px p-8px">
      <!-- 网络质量 -->
      <div
        class="relative overflow-hidden border rounded-8px p-6px backdrop-blur-sm transition-all duration-300 hover:shadow-lg"
        :style="{
          borderColor: chartData.networkQuality.borderColor,
          backgroundColor: chartData.networkQuality.bgColor,
          boxShadow: `0 0 15px ${chartData.networkQuality.bgColor}`
        }"
      >
        <!-- 装饰线条 -->
        <div
          class="absolute left-0 top-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
        ></div>
        <div class="absolute right-0 top-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>
        <div
          class="absolute bottom-0 right-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-l"
        ></div>
        <div class="absolute bottom-0 left-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-t"></div>

        <div ref="networkQualityRef" class="h-full w-full" />
      </div>

      <!-- 上网质量 -->
      <div
        class="relative overflow-hidden border rounded-8px p-6px backdrop-blur-sm transition-all duration-300 hover:shadow-lg"
        :style="{
          borderColor: chartData.internetQuality.borderColor,
          backgroundColor: chartData.internetQuality.bgColor,
          boxShadow: `0 0 15px ${chartData.internetQuality.bgColor}`
        }"
      >
        <!-- 装饰线条 -->
        <div
          class="absolute left-0 top-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
        ></div>
        <div class="absolute right-0 top-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>
        <div
          class="absolute bottom-0 right-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-l"
        ></div>
        <div class="absolute bottom-0 left-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-t"></div>

        <div ref="internetQualityRef" class="h-full w-full" />
      </div>

      <!-- 装维服务 -->
      <div
        class="relative overflow-hidden border rounded-8px p-6px backdrop-blur-sm transition-all duration-300 hover:shadow-lg"
        :style="{
          borderColor: chartData.maintenanceService.borderColor,
          backgroundColor: chartData.maintenanceService.bgColor,
          boxShadow: `0 0 15px ${chartData.maintenanceService.bgColor}`
        }"
      >
        <!-- 装饰线条 -->
        <div
          class="absolute left-0 top-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
        ></div>
        <div class="absolute right-0 top-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>
        <div
          class="absolute bottom-0 right-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-l"
        ></div>
        <div class="absolute bottom-0 left-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-t"></div>

        <div ref="maintenanceServiceRef" class="h-full w-full" />
      </div>

      <!-- 政企产品质量 -->
      <div
        class="relative overflow-hidden border rounded-8px p-6px backdrop-blur-sm transition-all duration-300 hover:shadow-lg"
        :style="{
          borderColor: chartData.enterpriseProduct.borderColor,
          backgroundColor: chartData.enterpriseProduct.bgColor,
          boxShadow: `0 0 15px ${chartData.enterpriseProduct.bgColor}`
        }"
      >
        <!-- 装饰线条 -->
        <div
          class="absolute left-0 top-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
        ></div>
        <div class="absolute right-0 top-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>
        <div
          class="absolute bottom-0 right-0 h-1px w-30% from-transparent via-blue-400 to-transparent bg-gradient-to-l"
        ></div>
        <div class="absolute bottom-0 left-0 h-30% w-1px from-blue-400 to-transparent bg-gradient-to-t"></div>

        <div ref="enterpriseProductRef" class="h-full w-full" />
      </div>
    </div>

    <!-- 全屏内容 - 数据大屏风格 -->
    <template #fullscreen-content>
      <div class="relative h-full overflow-hidden p-24px">
        <!-- 背景网格 -->
        <div class="bg-grid-pattern absolute inset-0 opacity-10"></div>

        <!-- 标题区域 -->
        <div class="relative mb-32px text-center">
          <div class="mb-16px text-6xl text-blue-400">
            <SvgIcon icon="mdi:chart-bar" />
          </div>
          <div class="text-2xl text-white font-600" style="text-shadow: 0 0 10px rgba(24, 144, 255, 0.8)">
            {{ props.title }} - 质量指标详情
          </div>
          <!-- 装饰线 -->
          <div
            class="absolute bottom-0 left-1/4 right-1/4 h-1px from-transparent via-blue-400 to-transparent bg-gradient-to-r"
          ></div>
        </div>

        <!-- 全屏数据表格 -->
        <div class="grid grid-cols-2 gap-24px">
          <!-- 网络服务质量 -->
          <div class="space-y-12px">
            <div class="mb-16px flex items-center">
              <div class="mr-8px h-16px w-4px bg-blue-400"></div>
              <h4 class="text-lg text-white font-600">网络服务质量</h4>
            </div>

            <!-- 网络质量 - 移动 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.networkQuality.borderColor,
                backgroundColor: chartData.networkQuality.bgColor,
                boxShadow: `0 0 10px ${chartData.networkQuality.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">网络质量 - 移动</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.networkQuality.data[0] }}%</span>
              </div>
            </div>

            <!-- 网络质量 - 电信 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.networkQuality.borderColor,
                backgroundColor: chartData.networkQuality.bgColor,
                boxShadow: `0 0 10px ${chartData.networkQuality.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">网络质量 - 电信</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.networkQuality.data[1] }}%</span>
              </div>
            </div>

            <!-- 上网质量 - 移动 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.internetQuality.borderColor,
                backgroundColor: chartData.internetQuality.bgColor,
                boxShadow: `0 0 10px ${chartData.internetQuality.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">上网质量 - 移动</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.internetQuality.data[0] }}%</span>
              </div>
            </div>

            <!-- 上网质量 - 电信 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.internetQuality.borderColor,
                backgroundColor: chartData.internetQuality.bgColor,
                boxShadow: `0 0 10px ${chartData.internetQuality.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">上网质量 - 电信</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.internetQuality.data[1] }}%</span>
              </div>
            </div>
          </div>

          <!-- 服务产品质量 -->
          <div class="space-y-12px">
            <div class="mb-16px flex items-center">
              <div class="mr-8px h-16px w-4px bg-blue-400"></div>
              <h4 class="text-lg text-white font-600">服务产品质量</h4>
            </div>

            <!-- 装维服务 - 移动 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.maintenanceService.borderColor,
                backgroundColor: chartData.maintenanceService.bgColor,
                boxShadow: `0 0 10px ${chartData.maintenanceService.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">装维服务 - 移动</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.maintenanceService.data[0] }}%</span>
              </div>
            </div>

            <!-- 装维服务 - 电信 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.maintenanceService.borderColor,
                backgroundColor: chartData.maintenanceService.bgColor,
                boxShadow: `0 0 10px ${chartData.maintenanceService.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">装维服务 - 电信</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.maintenanceService.data[1] }}%</span>
              </div>
            </div>

            <!-- 政企产品质量 - 移动 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.enterpriseProduct.borderColor,
                backgroundColor: chartData.enterpriseProduct.bgColor,
                boxShadow: `0 0 10px ${chartData.enterpriseProduct.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">政企产品质量 - 移动</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.enterpriseProduct.data[0] }}%</span>
              </div>
            </div>

            <!-- 政企产品质量 - 电信 -->
            <div
              class="relative overflow-hidden border rounded-8px px-16px py-12px transition-all duration-300 hover:shadow-lg"
              :style="{
                borderColor: chartData.enterpriseProduct.borderColor,
                backgroundColor: chartData.enterpriseProduct.bgColor,
                boxShadow: `0 0 10px ${chartData.enterpriseProduct.bgColor}`
              }"
            >
              <!-- 装饰线条 -->
              <div
                class="absolute left-0 top-0 h-1px w-20% from-transparent via-blue-400 to-transparent bg-gradient-to-r"
              ></div>
              <div class="absolute right-0 top-0 h-20% w-1px from-blue-400 to-transparent bg-gradient-to-b"></div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="mr-8px h-8px w-8px animate-pulse rounded-full bg-blue-400"></div>
                  <span class="text-white/90">政企产品质量 - 电信</span>
                </div>
                <span class="text-lg text-blue-400 font-600">{{ chartData.enterpriseProduct.data[1] }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>

<style scoped>
/* 背景网格样式 */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(24, 144, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(24, 144, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: center center;
}

/* 脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.8);
  }
}

.animate-pulse {
  animation: pulse 2s infinite ease-in-out;
}
</style>
