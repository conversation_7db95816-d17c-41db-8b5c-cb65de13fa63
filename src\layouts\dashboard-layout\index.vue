<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NConfigProvider } from 'naive-ui';
import { useThemeStore } from '@/store/modules/theme';
import { dashboardTheme } from '@/theme/dashboard-theme';
import GlobalContent from '../modules/global-content/index.vue';
import DashboardHeader from './modules/dashboard-header.vue';
import DashboardFooter from './modules/dashboard-footer.vue';
import DashboardContain from './modules/dashboard-contain.vue';

defineOptions({
  name: 'DashboardLayout'
});

const themeStore = useThemeStore();

// 合并全局主题和 dashboard 主题
const mergedTheme = computed(() => {
  return {
    ...themeStore.naiveTheme,
    ...dashboardTheme
  };
});

const isAnimating = ref(true);
const enableScale = ref(true);
const animationState = ref({
  headerVisible: false,
  contentVisible: false,
  footerVisible: false
});

// 处理缩放设置变更
const handleScaleChange = (value: boolean) => {
  enableScale.value = value;
};

onMounted(() => {
  // 页面进入动画序列
  setTimeout(() => {
    isAnimating.value = false;
    // 头部动画
    animationState.value.headerVisible = true;

    // 内容区域动画
    setTimeout(() => {
      animationState.value.contentVisible = true;

      // 底部动画
      setTimeout(() => {
        animationState.value.footerVisible = true;

        // 添加额外的延迟，确保所有图表能够正确渲染和调整大小
        setTimeout(() => {
          // 触发一次窗口大小变化事件，强制所有图表重新渲染
          window.dispatchEvent(new Event('resize'));
        }, 500);
      }, 400);
    }, 300);
  }, 100);
});
</script>

<template>
  <NConfigProvider :theme-overrides="mergedTheme" class="h-full">
    <div class="relative h-full w-full overflow-hidden" :class="{ 'no-scale': !enableScale }">
      <DashboardContain :enable-scale="enableScale">
        <!-- 顶部标题栏和导航栏 -->
        <Transition name="fade-slide-down">
          <div v-show="animationState.headerVisible">
            <DashboardHeader title="智能数据大屏" @update:scale="handleScaleChange" />
          </div>
        </Transition>

        <!-- 内容区域 -->
        <Transition name="fade-scale">
          <div
            v-show="animationState.contentVisible"
            class="relative z-1000 min-h-0 flex-1 bg-[url('@/assets/dashboard/imgs/dashboard-background.jpg')] bg-cover bg-center"
            :class="{ 'overflow-hidden': enableScale, 'overflow-auto dashboard-scrollbar': !enableScale }"
          >
            <div class="h-full">
              <GlobalContent :show-padding="false" />
            </div>
          </div>
        </Transition>

        <!-- 页脚 -->
        <Transition name="fade-slide-up">
          <DashboardFooter v-show="animationState.footerVisible" />
        </Transition>
      </DashboardContain>
    </div>
  </NConfigProvider>
</template>

<style scoped>
.bg-layout {
  background-color: transparent;
}

/* 入场动画效果 */
.fade-slide-down-enter-active,
.fade-slide-down-leave-active {
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.fade-slide-down-enter-from {
  opacity: 0;
  transform: translateY(-50px);
}
.fade-slide-down-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.fade-scale-enter-from {
  opacity: 0;
  transform: scale(0.9);
}
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

.fade-slide-up-enter-active,
.fade-slide-up-leave-active {
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.fade-slide-up-enter-from {
  opacity: 0;
  transform: translateY(50px);
}
.fade-slide-up-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
</style>
