<script setup lang="ts">
import { computed, ref } from 'vue';

/**
 * 大屏卡片组件属性
 */
interface Props {
  /** 卡片标题 */
  title: string;
  /** 标题图标 */
  titleIcon?: string;
  /** 是否显示全屏按钮 */
  showFullscreen?: boolean;
  /** 动画延迟 */
  animationDelay?: number;
  /** 自定义高度 */
  height?: number | string;
  /** 数据大屏风格主题 */
  theme?: 'blue' | 'green' | 'purple' | 'orange';
  /** 是否启用全屏内容模式（无padding） */
  fullContent?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  titleIcon: 'mdi:view-dashboard',
  height: 100,
  showFullscreen: false,
  animationDelay: 0,
  theme: 'blue',
  fullContent: false
});

const isHovered = ref(false);
const showFullscreenModal = ref(false);

// 主题配置
const themeConfig = computed(() => {
  const themes = {
    blue: {
      primary: '#1890ff',
      secondary: '#40a9ff',
      accent: '#69c0ff',
      glow: 'rgba(24, 144, 255, 0.4)',
      border: 'rgba(24, 144, 255, 0.3)',
      bg: 'rgba(24, 144, 255, 0.05)'
    },
    green: {
      primary: '#52c41a',
      secondary: '#73d13d',
      accent: '#95de64',
      glow: 'rgba(82, 196, 26, 0.4)',
      border: 'rgba(82, 196, 26, 0.3)',
      bg: 'rgba(82, 196, 26, 0.05)'
    },
    purple: {
      primary: '#722ed1',
      secondary: '#9254de',
      accent: '#b37feb',
      glow: 'rgba(114, 46, 209, 0.4)',
      border: 'rgba(114, 46, 209, 0.3)',
      bg: 'rgba(114, 46, 209, 0.05)'
    },
    orange: {
      primary: '#fa8c16',
      secondary: '#ffa940',
      accent: '#ffc069',
      glow: 'rgba(250, 140, 22, 0.4)',
      border: 'rgba(250, 140, 22, 0.3)',
      bg: 'rgba(250, 140, 22, 0.05)'
    }
  };
  return themes[props.theme];
});

// 计算卡片样式
const cardStyle = computed(() => {
  const style: Record<string, string> = {};

  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }

  if (props.animationDelay) {
    style.animationDelay = `${props.animationDelay}s`;
  }

  return style;
});

// 处理操作按钮点击
const handleFullscreen = () => {
  showFullscreenModal.value = true;
};
</script>

<template>
  <!-- 主卡片 -->
  <div
    :style="cardStyle"
    class="dashboard-card group relative h-full w-full flex flex-col overflow-hidden transition-all duration-500 ease-out"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- 科技感边框和背景 -->
    <div
      class="absolute inset-0 border-2 rounded-12px transition-all duration-500"
      :style="{
        borderColor: themeConfig.border,
        background: `linear-gradient(135deg, ${themeConfig.bg} 0%, rgba(0,0,0,0.1) 100%)`,
        boxShadow: isHovered
          ? `0 0 30px ${themeConfig.glow}, inset 0 0 20px ${themeConfig.bg}`
          : `0 8px 32px rgba(0,0,0,0.3)`
      }"
    />

    <!-- 科技装饰线条 -->
    <div class="absolute inset-0 overflow-hidden rounded-12px">
      <!-- 顶部装饰线 -->
      <div
        class="absolute left-0 top-0 h-1px w-full opacity-60"
        :style="{ background: `linear-gradient(90deg, transparent 0%, ${themeConfig.primary} 50%, transparent 100%)` }"
      />
      <!-- 左侧装饰线 -->
      <div
        class="absolute left-0 top-0 h-full w-1px opacity-40"
        :style="{
          background: `linear-gradient(180deg, transparent 0%, ${themeConfig.secondary} 50%, transparent 100%)`
        }"
      />
      <!-- 右上角装饰 -->
      <div
        class="absolute right-0 top-0 h-20px w-20px border-r-2 border-t-2 opacity-60"
        :style="{ borderColor: themeConfig.accent }"
      />
      <!-- 左下角装饰 -->
      <div
        class="absolute bottom-0 left-0 h-20px w-20px border-b-2 border-l-2 opacity-60"
        :style="{ borderColor: themeConfig.accent }"
      />
    </div>

    <!-- 卡片头部 -->
    <div class="relative z-10 h-56px flex items-center justify-between px-20px py-12px">
      <!-- 标题区域 -->
      <div class="flex items-center gap-12px">
        <!-- 状态指示器 -->
        <div class="relative flex items-center gap-8px">
          <div
            class="h-8px w-8px animate-pulse rounded-full"
            :style="{ backgroundColor: themeConfig.primary, boxShadow: `0 0 10px ${themeConfig.primary}` }"
          />
          <div class="h-1px w-16px" :style="{ backgroundColor: themeConfig.secondary }" />
        </div>

        <!-- 标题图标 -->
        <div
          v-if="titleIcon"
          class="h-32px w-32px flex items-center justify-center border rounded-6px text-20px backdrop-blur-sm"
          :style="{
            borderColor: themeConfig.border,
            backgroundColor: themeConfig.bg,
            color: themeConfig.primary
          }"
        >
          <SvgIcon :icon="titleIcon" />
        </div>

        <!-- 标题文字 -->
        <h3
          class="text-20px text-white font-600 tracking-wide"
          :style="{ textShadow: `0 0 8px ${themeConfig.primary}` }"
        >
          {{ title }}
        </h3>

        <!-- 标题额外内容 -->
        <slot name="title-extra" />
      </div>

      <!-- 操作按钮区域 -->
      <div class="flex items-center gap-8px">
        <!-- 自定义按钮插槽 -->
        <slot name="actions" />

        <!-- 全屏按钮 -->
        <NButton
          v-if="showFullscreen"
          text
          class="text-30px transition-all duration-200 hover:scale-110"
          :style="{ color: themeConfig.secondary }"
          @click="handleFullscreen"
        >
          <SvgIcon icon="mdi:fullscreen" />
        </NButton>
      </div>
    </div>

    <!-- 分割线 -->
    <div
      class="mx-20px h-1px opacity-30"
      :style="{ background: `linear-gradient(90deg, transparent 0%, ${themeConfig.primary} 50%, transparent 100%)` }"
    />

    <!-- 卡片内容 - 优化布局确保正确填充 -->
    <div class="relative z-10 min-h-0 flex-1 overflow-hidden p-3">
      <slot />
    </div>
  </div>

  <!-- 全屏对话框 -->
  <DashboardDialog
    v-model:visible="showFullscreenModal"
    :title="`${title} - 全屏视图`"
    :title-icon="titleIcon"
    width="95vw"
    height="90vh"
    :show-footer="false"
    :full-content="fullContent"
    :mask-closable="true"
  >
    <!-- 全屏内容 -->
    <div class="h-full w-full">
      <slot name="fullscreen-content">
        <div class="h-full flex items-center justify-center">
          <div class="text-center">
            <NIcon size="48" class="mb-16px text-gray-400">
              <SvgIcon icon="mdi:fullscreen" />
            </NIcon>
            <p class="text-gray-500">全屏内容区域</p>
            <p class="text-sm text-gray-400">请使用 fullscreen-content 插槽自定义内容</p>
          </div>
        </div>
      </slot>
    </div>
  </DashboardDialog>
</template>
