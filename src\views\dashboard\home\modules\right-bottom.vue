<script setup lang="ts">
defineOptions({
  name: 'RightBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <div class="h-80% flex items-center justify-center">
      <slot>
        <div class="w-full text-center">
          <div class="mb-16px text-4xl text-orange-400">📈</div>
          <div class="mb-16px text-lg text-white/90 font-600">趋势预测</div>

          <!-- 简单的趋势线 -->
          <div class="relative mb-16px h-24">
            <svg class="h-full w-full" viewBox="0 0 200 60">
              <defs>
                <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color: #fa8c16; stop-opacity: 0.3" />
                  <stop offset="100%" style="stop-color: #fa8c16; stop-opacity: 0" />
                </linearGradient>
              </defs>
              <path
                d="M10,50 Q50,30 90,35 T170,20"
                stroke="#fa8c16"
                stroke-width="2"
                fill="none"
                stroke-linecap="round"
              />
              <path d="M10,50 Q50,30 90,35 T170,20 L170,60 L10,60 Z" fill="url(#trendGradient)" />
              <!-- 数据点 -->
              <circle cx="10" cy="50" r="3" fill="#fa8c16" />
              <circle cx="50" cy="35" r="3" fill="#fa8c16" />
              <circle cx="90" cy="40" r="3" fill="#fa8c16" />
              <circle cx="130" cy="25" r="3" fill="#fa8c16" />
              <circle cx="170" cy="20" r="3" fill="#fa8c16" />
            </svg>
          </div>

          <div class="flex justify-between text-sm">
            <div class="text-center">
              <div class="text-orange-400 font-600">+12%</div>
              <div class="text-white/50">本月</div>
            </div>
            <div class="text-center">
              <div class="text-green-400 font-600">+25%</div>
              <div class="text-white/50">预测</div>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <div class="mb-32px text-center">
          <div class="mb-16px text-6xl text-orange-400">📈</div>
          <div class="text-2xl text-white/90">{{ props.title }} - 详细趋势分析</div>
        </div>

        <div class="grid grid-cols-1 gap-32px">
          <!-- 大趋势图 -->
          <div class="relative h-64">
            <svg class="h-full w-full" viewBox="0 0 800 200">
              <defs>
                <linearGradient id="fullTrendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color: #fa8c16; stop-opacity: 0.4" />
                  <stop offset="100%" style="stop-color: #fa8c16; stop-opacity: 0" />
                </linearGradient>
              </defs>
              <!-- 网格线 -->
              <defs>
                <pattern id="grid" width="80" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 80 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />

              <!-- 趋势线 -->
              <path
                d="M40,160 Q120,140 200,130 T360,110 T520,90 T680,70 T760,50"
                stroke="#fa8c16"
                stroke-width="3"
                fill="none"
                stroke-linecap="round"
              />
              <path
                d="M40,160 Q120,140 200,130 T360,110 T520,90 T680,70 T760,50 L760,200 L40,200 Z"
                fill="url(#fullTrendGradient)"
              />

              <!-- 数据点 -->
              <circle cx="40" cy="160" r="4" fill="#fa8c16" />
              <circle cx="160" cy="135" r="4" fill="#fa8c16" />
              <circle cx="280" cy="120" r="4" fill="#fa8c16" />
              <circle cx="400" cy="105" r="4" fill="#fa8c16" />
              <circle cx="520" cy="90" r="4" fill="#fa8c16" />
              <circle cx="640" cy="75" r="4" fill="#fa8c16" />
              <circle cx="760" cy="50" r="4" fill="#fa8c16" />
            </svg>
          </div>

          <!-- 统计数据 -->
          <div class="grid grid-cols-4 gap-24px text-center">
            <div>
              <div class="text-2xl text-orange-400 font-600">+15.2%</div>
              <div class="text-white/60">月增长率</div>
            </div>
            <div>
              <div class="text-2xl text-green-400 font-600">+28.5%</div>
              <div class="text-white/60">季度预测</div>
            </div>
            <div>
              <div class="text-2xl text-blue-400 font-600">92.3%</div>
              <div class="text-white/60">预测准确率</div>
            </div>
            <div>
              <div class="text-2xl text-purple-400 font-600">7天</div>
              <div class="text-white/60">预测周期</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
